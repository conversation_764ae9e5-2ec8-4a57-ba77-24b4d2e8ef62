import type { VisitedPlace } from './wanderlust';
import type { RouteWaypoint, TrafficInfo } from './route-planning';

// Base types for route planning
export interface BaseMarkerConfig {
  icon: string | google.maps.Symbol | google.maps.Icon;
  label?: google.maps.MarkerLabel;
  animation?: google.maps.Animation;
  zIndex?: number;
  title?: string;
}

export interface BaseMapFeatures {
  markers: boolean;
  traffic: boolean;
  routes: boolean;
  clustering: boolean;
}

// Map context types
export interface MapContextState {
  center: google.maps.LatLngLiteral;
  zoom: number;
  mapInstance: google.maps.Map | null;
  isLoading: boolean;
  error: Error | null;
}

export type MapMode = 'route-planning' | 'venue-discovery' | 'travel-history' | 'general';

export interface MarkerContext {
  mode: MapMode;
  isSelected: boolean;
  index?: number;
  totalCount: number;
  isStart?: boolean;
  isEnd?: boolean;
  waypointData?: RouteWaypoint;
}

export interface MarkerConfig {
  icon: string | google.maps.Symbol | google.maps.Icon;
  label?: google.maps.MarkerLabel;
  animation?: google.maps.Animation;
  zIndex?: number;
  title?: string;
}

export interface EnhancedRouteMapProps {
  className?: string;
  showWaypointOverlay?: boolean;
  showTraffic?: boolean;
  onMapLoad?: () => void;
  onWaypointDrop?: (place: VisitedPlace, index: number) => void;
  onWaypointRemove?: (waypointId: string) => void;
  onWaypointReorder?: (sourceIndex: number, targetIndex: number) => void;
  onRouteCalculated?: (waypoints: RouteWaypoint[]) => void;
  onTrafficUpdate?: (info: TrafficInfo) => void;
  waypoints?: RouteWaypoint[];
  maxWaypoints?: number;
  enableRouteOptimization?: boolean;
  showTrafficOnPolylines?: boolean;
  allowWaypointReordering?: boolean;
  onWaypointAdd?: (waypoint: RouteWaypoint) => void;
  onRouteOptimize?: (waypoints: RouteWaypoint[]) => void;
  routeColor?: string;
  waypointStyle?: 'numbered' | 'lettered' | 'custom';
  showETA?: boolean;
  showDistance?: boolean;
  children?: React.ReactNode;
}
