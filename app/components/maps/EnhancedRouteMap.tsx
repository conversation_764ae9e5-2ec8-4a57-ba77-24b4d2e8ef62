/**
 * Enhanced RouteMap Component
 * 
 * A specialized map component for route planning that extends BaseMapComponent
 * with features like waypoint management, traffic visualization, and route optimization.
 */

import React, { useCallback, useState, useEffect, useMemo } from 'react';
import { BaseMapComponent, type BaseMapComponentProps } from '../shared/BaseMapComponent';
import { WaypointManagementOverlay } from './WaypointManagementOverlay';
import { TrafficAwareRouteLayer } from './TrafficAwareRouteLayer';
import { RouteOptimizationPanel } from './RouteOptimizationPanel';
import { useMapContext } from '../shared/MapProvider';
import { showSuccess, showWarning } from '~/components/wanderlust/NotificationSystem';
import type { VisitedPlace } from '~/types/wanderlust';
import type { RouteWaypoint, TrafficInfo } from '~/types/route-planning';
import type { MarkerConfig, MarkerContext } from '~/types/maps';
import type { WaypointManagementOverlayProps } from '~/types/waypoint-management';
import { cn } from '~/lib/utils';

interface EnhancedRouteMapProps extends Pick<BaseMapComponentProps, 'center' | 'zoom' | 'mapTypeId' | 'options' | 'onLoad' | 'onClick' | 'onError'> {
  className?: string;
  waypoints?: VisitedPlace[];
  maxWaypoints?: number;
  enableRouteOptimization?: boolean;
  showTrafficOnPolylines?: boolean;
  allowWaypointReordering?: boolean;
  showWaypointOverlay?: boolean;
  showTraffic?: boolean;
  onMapLoad?: () => void;
  onWaypointAdd?: (waypoint: RouteWaypoint) => void;
  onWaypointRemove?: (waypointId: string) => void;
  onWaypointReorder?: (waypoints: RouteWaypoint[]) => void;
  onRouteOptimize?: (waypoints: RouteWaypoint[]) => void;
  onRouteCalculated?: (waypoints: RouteWaypoint[]) => void;
  onTrafficUpdate?: (info: TrafficInfo) => void;
  routeColor?: string;
  waypointStyle?: 'numbered' | 'lettered' | 'custom';
  showETA?: boolean;
  showDistance?: boolean;
  children?: React.ReactNode;
}

// Route-specific waypoint marker renderer
const routeWaypointRenderer = (place: VisitedPlace, context: MarkerContext): MarkerConfig => {
  const waypoint = place as unknown as RouteWaypoint & { isStart?: boolean; isEnd?: boolean };
  const { isSelected, index = 0 } = context;
  
  // Start point (green)
  if (waypoint.isStart) {
    return {
      icon: {
        path: google.maps.SymbolPath.CIRCLE,
        fillColor: '#10B981',
        fillOpacity: 1,
        strokeColor: '#ffffff',
        strokeWeight: 2,
        scale: isSelected ? 12 : 10,
      },
      animation: isSelected ? google.maps.Animation.BOUNCE : undefined,
      zIndex: waypoint.isStart ? 1000 : (waypoint.isEnd ? 999 : 100 + index),
      title: `Start: ${waypoint.name}`,
    };
  }
  
  // End point (red)
  if (waypoint.isEnd) {
    return {
      icon: {
        path: google.maps.SymbolPath.CIRCLE,
        fillColor: '#DC2626',
        fillOpacity: 1,
        strokeColor: '#ffffff',
        strokeWeight: 2,
        scale: isSelected ? 12 : 10,
      },
      animation: isSelected ? google.maps.Animation.BOUNCE : undefined,
      zIndex: waypoint.isEnd ? 999 : 100 + index,
      title: `End: ${waypoint.name}`,
    };
  }
  
  // Intermediate waypoints (numbered, blue)
  return {
    icon: {
      url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="14" fill="#3B82F6" stroke="#ffffff" stroke-width="2"/>
          <text x="16" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
            ${(waypoint.waypointIndex ?? 0) + 1}
          </text>
        </svg>
      `)}`,
      scaledSize: new google.maps.Size(isSelected ? 36 : 32, isSelected ? 36 : 32),
      anchor: new google.maps.Point(isSelected ? 18 : 16, isSelected ? 18 : 16),
    },
    animation: isSelected ? google.maps.Animation.BOUNCE : undefined,
    zIndex: 100 + index,
    title: `Waypoint ${(waypoint.waypointIndex ?? 0) + 1}: ${waypoint.name}`,
  };
};

// Helper functions for type conversion
const visitedPlaceToRouteWaypoint = (place: VisitedPlace, index: number, totalPlaces: number): RouteWaypoint => ({
  ...place,
  location: {
    lat: place.coordinates.latitude,
    lng: place.coordinates.longitude,
  },
  placeData: place, // Store original VisitedPlace for PlaceManagementPanel
  stopover: index > 0,
  waypointIndex: index,
  isStart: index === 0,
  isEnd: index === totalPlaces - 1,
  estimatedTravelTime: 0, // Placeholder, will be updated by route calculation
});

export function EnhancedRouteMap({
  waypoints = [],
  maxWaypoints = 25,
  enableRouteOptimization = true,
  showTrafficOnPolylines = true,
  allowWaypointReordering = true,
  onWaypointAdd,
  onWaypointRemove,
  onWaypointReorder,
  onRouteOptimize,
  routeColor = '#3B82F6',
  waypointStyle = 'numbered',
  showETA = true,
  showDistance = true,
  className,
  children,
  ...baseProps
}: EnhancedRouteMapProps) {
  const mapContext = useMapContext();
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [showOptimizationPanel, setShowOptimizationPanel] = useState(false);

  // Convert waypoints to RouteWaypoint format if needed
const { places, setPlaces } = useMapContext();

const routeWaypoints = useMemo(() => {
  return places.map((waypoint, index) => 
    visitedPlaceToRouteWaypoint(waypoint, index, places.length)
  );
}, [places]);

  // Handle waypoint selection from map
  const handleWaypointSelection = useCallback((place: VisitedPlace) => {
    // Check if we've reached the waypoint limit
    if (waypoints.length >= maxWaypoints) {
      showWarning('Waypoint Limit', `Maximum ${maxWaypoints} waypoints allowed`);
      return;
    }

    // Convert to RouteWaypoint
    const newWaypoint = visitedPlaceToRouteWaypoint(place, waypoints.length, waypoints.length + 1);

    onWaypointAdd?.(newWaypoint);
    showSuccess('Waypoint Added', `${place.name} added to route`);
  }, [waypoints.length, maxWaypoints, onWaypointAdd]);

  // Handle waypoint removal
const handleWaypointRemove = useCallback((waypointId: string) => {
  const newPlaces = places.filter(place => place.id !== waypointId);
  setPlaces(newPlaces);
  showSuccess('Waypoint Removed', 'Waypoint removed from route');
}, [places, setPlaces]);

  // Handle waypoint reordering
const handleWaypointReorder = useCallback((sourceIndex: number, targetIndex: number) => {
  const newPlaces = [...places];
  const [removed] = newPlaces.splice(sourceIndex, 1);
  newPlaces.splice(targetIndex, 0, removed);
  
  // Update waypoint indices
  const updatedPlaces = newPlaces.map((place, index) => ({
    ...place,
    waypointIndex: index,
    isStart: index === 0,
    isEnd: index === newPlaces.length - 1,
  }));

  setPlaces(updatedPlaces);
  showSuccess('Route Updated', 'Waypoints reordered successfully');
}, [places, setPlaces]);

  // Handle route optimization
  const handleRouteOptimize = useCallback(async () => {
    if (waypoints.length < 3) {
      showWarning('Optimization Unavailable', 'Need at least 3 waypoints to optimize route');
      return;
    }

    setIsOptimizing(true);
    try {
      // Simulate route optimization logic
      // In a real implementation, this would call Google Maps Directions API
      // with optimizeWaypoints: true
      
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      
      // For demo purposes, reverse the middle waypoints
      const optimizedWaypoints = routeWaypoints.map((wp, i) => ({
        ...wp,
        waypointIndex: i
      }));
      if (optimizedWaypoints.length > 2) {
        const middle = optimizedWaypoints.slice(1, -1);
        middle.reverse();
        optimizedWaypoints.splice(1, middle.length, ...middle);
      }

      onRouteOptimize?.(optimizedWaypoints);
      showSuccess('Route Optimized', 'Route has been optimized for best travel time');
    } catch (error) {
      console.error('Route optimization failed:', error);
    } finally {
      setIsOptimizing(false);
    }
  }, [routeWaypoints, onRouteOptimize]);

  // Calculate route statistics
  const routeStats = useMemo(() => {
    const totalDistance = routeWaypoints.reduce((sum, waypoint) => 
      sum + (waypoint.estimatedTravelTime || 0), 0);
    const totalTrafficDelay = routeWaypoints.reduce((sum, waypoint) => 
      sum + (waypoint.trafficDelay || 0), 0);
    
    return {
      totalWaypoints: routeWaypoints.length,
      estimatedDuration: totalDistance,
      trafficDelay: totalTrafficDelay,
      optimizable: routeWaypoints.length >= 3,
    };
  }, [routeWaypoints]);

  return (
    <div className={cn("relative h-full", className)}>
      <BaseMapComponent
        features={['markers', 'routes', 'search', 'traffic']}
        customMarkerRenderer={routeWaypointRenderer}
        onPlaceSelect={handleWaypointSelection}
        showTrafficLayer={showTrafficOnPolylines}
        className="h-full"
        onMapLoad={baseProps.onMapLoad}
        onError={baseProps.onError}
        mapTypeId={baseProps.mapTypeId}
        center={baseProps.center}
        zoom={baseProps.zoom}
        options={baseProps.options}
        onLoad={baseProps.onLoad}
        onClick={baseProps.onClick}
        currentRoute={null}
        places={routeWaypoints}
      >
        {/* Traffic-Aware Route Layer */}
        <TrafficAwareRouteLayer
          waypoints={routeWaypoints}
          routeColor={routeColor}
          showTraffic={showTrafficOnPolylines}
          showETA={showETA}
          showDistance={showDistance}
        />

        {/* Waypoint Management Overlay */}
        <WaypointManagementOverlay
          onWaypointRemove={handleWaypointRemove}
          onWaypointReorder={handleWaypointReorder}
        />

        {/* Route Optimization Panel */}
        {enableRouteOptimization && showOptimizationPanel && (
          <RouteOptimizationPanel
            waypoints={routeWaypoints}
            isOptimizing={isOptimizing}
            routeStats={routeStats}
            onOptimize={handleRouteOptimize}
            onClose={() => setShowOptimizationPanel(false)}
          />
        )}

        {/* Route Controls Overlay */}
        <div className="absolute top-4 right-4 z-10 space-y-2">
          {/* Route Stats Display */}
          {routeWaypoints.length > 0 && (
            <div className="bg-black/80 backdrop-blur-sm rounded-lg p-3 text-white text-sm border border-[#FFD700]/30">
              <div className="flex items-center space-x-4">
                <span>📍 {routeStats.totalWaypoints} stops</span>
                {showETA && routeStats.estimatedDuration > 0 && (
                  <span>⏱️ {Math.round(routeStats.estimatedDuration)}min</span>
                )}
                {showTrafficOnPolylines && routeStats.trafficDelay > 0 && (
                  <span className="text-yellow-400">🚦 +{routeStats.trafficDelay}min</span>
                )}
              </div>
            </div>
          )}

          {/* Optimization Button */}
          {enableRouteOptimization && routeStats.optimizable && (
            <button
              onClick={() => setShowOptimizationPanel(true)}
              disabled={isOptimizing}
              className="bg-[#3B82F6] hover:bg-[#2563EB] disabled:opacity-50 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors border border-[#FFD700]/30"
            >
              {isOptimizing ? (
                <>
                  <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Optimizing...
                </>
              ) : (
                '🎯 Optimize Route'
              )}
            </button>
          )}
        </div>

        {/* Custom children */}
        {children}
      </BaseMapComponent>
    </div>
  );
}
