/**
 * Enhanced Route Layer Component
 *
 * Integrates with Routes v2 for advanced polyline rendering with FIFA design system styling.
 * Features traffic-aware polylines, interactive route selection, and enhanced visual feedback.
 */

import React, { useMemo, useCallback, useState } from 'react';
import { <PERSON><PERSON><PERSON>, Marker } from '@react-google-maps/api';
import { useMapContext } from '../MapProvider';
import type { EnhancedTravelRoute } from '~/types/routes-v2';
import type { RouteWaypoint, TravelRoute } from '~/types/wanderlust';

export interface RouteLayerProps {
  route?: EnhancedTravelRoute | TravelRoute;
  alternativeRoutes?: (EnhancedTravelRoute | TravelRoute)[];
  waypoints?: RouteWaypoint[];
  onRouteClick?: (route: EnhancedTravelRoute | TravelRoute) => void;
  onWaypointClick?: (waypoint: RouteWaypoint, index: number) => void;
  onRouteSelect?: (route: EnhancedTravelRoute | TravelRoute, index: number) => void;
  showAlternatives?: boolean;
  showWaypoints?: boolean;
  showTrafficOnPolylines?: boolean;
  enableRouteComparison?: boolean;
  selectedRouteIndex?: number;
  className?: string;
}

// FIFA Club World Cup 2025™ route colors with traffic awareness
const ROUTE_COLORS = {
  primary: '#DC2626',      // FIFA Red
  primaryHover: '#B91C1C', // Darker FIFA Red
  alternative: '#6B7280',  // Gray
  alternativeHover: '#4B5563', // Darker Gray
  alternativeSelected: '#DC2626', // FIFA Red when selected
  waypoint: '#FFD700',     // FIFA Gold
  waypointBorder: '#FFFFFF', // White border
  // Traffic colors
  trafficNormal: '#10B981',    // Green for normal traffic
  trafficSlow: '#F59E0B',      // Amber for slow traffic
  trafficHeavy: '#EF4444',     // Red for heavy traffic
  trafficUnknown: '#6B7280',   // Gray for unknown traffic
};

// Traffic speed thresholds (km/h)
const TRAFFIC_THRESHOLDS = {
  normal: 40,
  slow: 20,
  heavy: 10,
};

export function RouteLayer({
  route: propRoute,
  alternativeRoutes: propAlternativeRoutes,
  waypoints: propWaypoints,
  onRouteClick,
  onWaypointClick,
  onRouteSelect,
  showAlternatives = true,
  showWaypoints = true,
  showTrafficOnPolylines = false,
  enableRouteComparison = false,
  selectedRouteIndex = 0,
  className,
}: RouteLayerProps) {
  const {
    currentRoute,
    alternativeRoutes,
    isFeatureEnabled,
  } = useMapContext();

  // State for hover interactions
  const [hoveredRouteIndex, setHoveredRouteIndex] = useState<number | null>(null);

  // Use routes from props or context
  const primaryRoute = propRoute || currentRoute;
  const altRoutes = propAlternativeRoutes || alternativeRoutes;

  // Check if routes feature is enabled
  const shouldShowRoutes = isFeatureEnabled('routes');

  // Decode polyline string to path coordinates
  const decodePolyline = useCallback((polyline: string): google.maps.LatLng[] => {
    if (!polyline || !window.google?.maps?.geometry?.encoding) {
      return [];
    }

    try {
      return google.maps.geometry.encoding.decodePath(polyline);
    } catch (error) {
      console.error('Error decoding polyline:', error);
      return [];
    }
  }, []);



  // Get traffic color based on speed
  const getTrafficColor = useCallback((speedKmh?: number): string => {
    if (!speedKmh) return ROUTE_COLORS.trafficUnknown;

    if (speedKmh >= TRAFFIC_THRESHOLDS.normal) return ROUTE_COLORS.trafficNormal;
    if (speedKmh >= TRAFFIC_THRESHOLDS.slow) return ROUTE_COLORS.trafficSlow;
    if (speedKmh >= TRAFFIC_THRESHOLDS.heavy) return ROUTE_COLORS.trafficHeavy;
    return ROUTE_COLORS.trafficHeavy;
  }, []);

  // Create traffic-aware polyline segments
  const createTrafficAwarePolyline = useCallback((
    route: EnhancedTravelRoute | TravelRoute,
    isAlternative: boolean = false,
    routeIndex: number = 0
  ) => {
    if (!route.polyline) {
      return null;
    }

    const path = decodePolyline(route.polyline);
    if (path.length === 0) {
      return null;
    }

    // Check if we have valid steps for traffic-aware rendering
    const hasValidSteps = route.legs?.some(leg => leg.steps && leg.steps.length > 0);

    // If traffic data is not available or disabled, or no valid steps, use standard polyline
    if (!showTrafficOnPolylines || !route.legs?.length || !hasValidSteps) {
      const isHovered = hoveredRouteIndex === routeIndex;
      const isSelected = enableRouteComparison && selectedRouteIndex === routeIndex;

      let strokeColor = isAlternative ? ROUTE_COLORS.alternative : ROUTE_COLORS.primary;
      let strokeWeight = isAlternative ? 2 : 4;
      let strokeOpacity = isAlternative ? 0.6 : 0.8;

      if (isAlternative && (isHovered || isSelected)) {
        strokeColor = isSelected ? ROUTE_COLORS.alternativeSelected : ROUTE_COLORS.alternativeHover;
        strokeWeight = isSelected ? 4 : 3;
        strokeOpacity = 0.9;
      }



      return (
        <Polyline
          key={`route-${routeIndex}`}
          path={path}
          options={{
            strokeColor,
            strokeWeight,
            strokeOpacity,
            zIndex: isAlternative ? (isHovered || isSelected ? 800 : 500) : 1000,
            clickable: !!onRouteClick || !!onRouteSelect,
          }}
          onClick={() => {
            onRouteClick?.(route);
            onRouteSelect?.(route, routeIndex);
          }}
          onMouseOver={() => setHoveredRouteIndex(routeIndex)}
          onMouseOut={() => setHoveredRouteIndex(null)}
        />
      );
    }

    // Create traffic-aware segments
    const segments: React.ReactElement[] = [];
    let segmentIndex = 0;

    route.legs?.forEach((leg, legIndex) => {
      leg.steps?.forEach((step, stepIndex) => {
        if (!step.polyline) return;

        // Handle different polyline structures
        let polylineString = '';
        if (typeof step.polyline === 'string') {
          polylineString = step.polyline;
        } else if (step.polyline.points) {
          polylineString = step.polyline.points;
        } else if ((step.polyline as any).encodedPolyline) {
          polylineString = (step.polyline as any).encodedPolyline;
        }

        if (!polylineString) return;

        const segmentPath = decodePolyline(polylineString);
        if (segmentPath.length === 0) return;

        // Estimate speed from step data (this would ideally come from Routes API traffic data)
        const estimatedSpeed = step.duration?.value && step.distance?.value
          ? (step.distance.value / 1000) / (step.duration.value / 3600) // km/h
          : undefined;

        const trafficColor = getTrafficColor(estimatedSpeed);
        const isHovered = hoveredRouteIndex === routeIndex;
        const isSelected = enableRouteComparison && selectedRouteIndex === routeIndex;

        segments.push(
          <Polyline
            key={`traffic-segment-${routeIndex}-${legIndex}-${stepIndex}`}
            path={segmentPath}
            options={{
              strokeColor: trafficColor,
              strokeWeight: isAlternative ? (isHovered || isSelected ? 3 : 2) : 4,
              strokeOpacity: isAlternative ? (isHovered || isSelected ? 0.9 : 0.7) : 0.8,
              zIndex: isAlternative ? (isHovered || isSelected ? 800 : 500) : 1000,
              clickable: !!onRouteClick || !!onRouteSelect,
            }}
            onClick={() => {
              onRouteClick?.(route);
              onRouteSelect?.(route, routeIndex);
            }}
            onMouseOver={() => setHoveredRouteIndex(routeIndex)}
            onMouseOut={() => setHoveredRouteIndex(null)}
          />
        );
        segmentIndex++;
      });
    });

    return segments;
  }, [
    decodePolyline,
    getTrafficColor,
    showTrafficOnPolylines,
    hoveredRouteIndex,
    enableRouteComparison,
    selectedRouteIndex,
    onRouteClick,
    onRouteSelect,
  ]);



  // Handle waypoint click
  const handleWaypointClick = useCallback((waypoint: RouteWaypoint, index: number) => {
    onWaypointClick?.(waypoint, index);
  }, [onWaypointClick]);

  // Create waypoint marker icon
  const createWaypointIcon = useCallback((_index: number, isStart: boolean, isEnd: boolean): google.maps.Symbol => {
    let fillColor = ROUTE_COLORS.waypoint;
    let strokeColor = ROUTE_COLORS.waypointBorder;
    let scale = 10;

    if (isStart) {
      fillColor = '#059669'; // Green for start
      scale = 12;
    } else if (isEnd) {
      fillColor = '#DC2626'; // Red for end
      scale = 12;
    }

    return {
      path: google.maps.SymbolPath.CIRCLE,
      fillColor,
      fillOpacity: 0.9,
      strokeColor,
      strokeWeight: 2,
      scale,
      anchor: new google.maps.Point(0, 0),
    };
  }, []);

  // Primary route polyline with traffic awareness
  const primaryRoutePolyline = useMemo(() => {
    if (!shouldShowRoutes || !primaryRoute) {
      return null;
    }

    return createTrafficAwarePolyline(primaryRoute, false, 0);
  }, [shouldShowRoutes, primaryRoute, createTrafficAwarePolyline]);

  // Alternative routes polylines with traffic awareness and interaction
  const alternativeRoutePolylines = useMemo(() => {
    if (!shouldShowRoutes || !showAlternatives || !altRoutes?.length) {
      return null;
    }

    return altRoutes.map((route, index) => {
      if (!route.polyline) return null;

      return createTrafficAwarePolyline(route, true, index + 1);
    }).filter(Boolean);
  }, [shouldShowRoutes, showAlternatives, altRoutes, createTrafficAwarePolyline]);

  // Waypoint markers
  const waypointMarkers = useMemo(() => {
    if (!shouldShowRoutes || !showWaypoints || !propWaypoints?.length) {
      return null;
    }

    return propWaypoints.map((waypoint, index) => {
      const isStart = index === 0;
      const isEnd = index === propWaypoints.length - 1;

      return (
        <Marker
          key={`waypoint-${index}`}
          position={waypoint.location}
          icon={createWaypointIcon(index, isStart, isEnd)}
          onClick={() => handleWaypointClick(waypoint, index)}
          title={isStart ? 'Start' : isEnd ? 'End' : `Waypoint ${index}`}
          zIndex={2000}
        />
      );
    });
  }, [shouldShowRoutes, showWaypoints, propWaypoints, createWaypointIcon, handleWaypointClick]);

  // Numbered waypoint markers (for route planning)
  const numberedWaypoints = useMemo(() => {
    if (!shouldShowRoutes || !showWaypoints || !propWaypoints?.length) {
      return null;
    }

    return propWaypoints.map((waypoint, index) => {
      const isStart = index === 0;
      const isEnd = index === propWaypoints.length - 1;

      // Skip numbered markers for start/end if they're already styled differently
      if (isStart || isEnd) return null;

      return (
        <Marker
          key={`numbered-waypoint-${index}`}
          position={waypoint.location}
          icon={{
            url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
              <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" fill="${ROUTE_COLORS.waypoint}" stroke="${ROUTE_COLORS.waypointBorder}" stroke-width="2"/>
                <text x="12" y="16" text-anchor="middle" fill="#000" font-family="Arial, sans-serif" font-size="12" font-weight="bold">${index + 1}</text>
              </svg>
            `)}`,
            scaledSize: new google.maps.Size(24, 24),
            anchor: new google.maps.Point(12, 12),
          }}
          onClick={() => handleWaypointClick(waypoint, index)}
          title={`Stop ${index + 1}`}
          zIndex={2000}
        />
      );
    });
  }, [shouldShowRoutes, showWaypoints, propWaypoints, handleWaypointClick]);

  if (!shouldShowRoutes) {
    return null;
  }

  return (
    <div className={className}>
      {/* Primary route */}
      {primaryRoutePolyline}

      {/* Alternative routes */}
      {alternativeRoutePolylines}

      {/* Waypoint markers */}
      {waypointMarkers}

      {/* Numbered waypoints */}
      {numberedWaypoints}
    </div>
  );
}

// Helper function to calculate route bounds
export function calculateRouteBounds(route: EnhancedTravelRoute): google.maps.LatLngBounds | null {
  if (!route.polyline || !window.google?.maps) {
    return null;
  }

  try {
    const path = google.maps.geometry.encoding.decodePath(route.polyline);
    if (path.length === 0) return null;

    const bounds = new google.maps.LatLngBounds();
    path.forEach(point => bounds.extend(point));

    return bounds;
  } catch (error) {
    console.error('Error calculating route bounds:', error);
    return null;
  }
}

// Helper function to get route color based on type
export function getRouteColor(isPrimary: boolean = true): string {
  return isPrimary ? ROUTE_COLORS.primary : ROUTE_COLORS.alternative;
}
