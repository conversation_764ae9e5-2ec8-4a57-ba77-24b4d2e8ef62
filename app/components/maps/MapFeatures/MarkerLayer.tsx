/**
 * Marker Layer Component
 *
 * Handles place markers with category-based styling and FIFA design system.
 * Provides interactive markers with info windows and selection state.
 */

import React, { useCallback, useMemo } from 'react';
import { Marker, InfoWindow } from '@react-google-maps/api';
import { useMapContext } from '../MapProvider';
import type { VisitedPlace } from '~/types/wanderlust';
import type { MarkerContext, MarkerConfig, MapMode } from '~/types/maps';

// Category colors following FIFA Club World Cup 2025™ design system
const categoryColors: Record<string, string> = {
  food: '#FFD700',        // FIFA Gold
  landmark: '#DC2626',    // FIFA Red
  museum: '#1E40AF',      // Deep Blue
  park: '#059669',        // Emerald Green
  accommodation: '#7C2D12', // Brown
  transport: '#374151',   // Gray
  entertainment: '#BE185D', // Pink
  shopping: '#EA580C',    // Orange
  default: '#FFD700',     // FIFA Gold as default
};

export interface MarkerLayerProps {
  places?: VisitedPlace[];
  onMarkerClick?: (place: VisitedPlace) => void;
  onInfoWindowClose?: () => void;
  showInfoWindow?: boolean;
  // Corrected type definition for customMarkerIcon
  customMarkerIcon?: (place: VisitedPlace, context: MarkerContext) => MarkerConfig;
  className?: string;
  mode?: MapMode; // Add mode prop
}

export function MarkerLayer({
  places: propPlaces,
  onMarkerClick,
  onInfoWindowClose,
  showInfoWindow = true,
  customMarkerIcon,
  className,
  mode = 'general', // Access mode from props
}: MarkerLayerProps) {
  const {
    places: contextPlaces,
    selectedPlace,
    setSelectedPlace,
    isFeatureEnabled,
    // Removed mode from context destructuring
  } = useMapContext();

  // Use places from props or context
  const places = propPlaces || contextPlaces;

  // Check if markers feature is enabled
  const shouldShowMarkers = isFeatureEnabled('markers');

  // Debug logging for marker visibility
  if (places?.length) {
    console.log('🎯 MarkerLayer: Rendering', places.length, 'markers, feature enabled:', shouldShowMarkers);
  }

  // Create marker context for custom renderers
  const createMarkerContext = useCallback((place: VisitedPlace, index: number): MarkerContext => ({
    mode, // Use mode from props
    isSelected: selectedPlace?.id === place.id,
    index,
    totalCount: places?.length ?? 0,
    isStart: false, // Default values for additional fields
    isEnd: false,
    waypointData: undefined
  }), [mode, selectedPlace, places?.length]);


  // Create marker icon based on category or custom renderer
  const createMarkerIcon = useCallback((place: VisitedPlace, context: MarkerContext): google.maps.Symbol | google.maps.Icon | string => {
    if (customMarkerIcon) {
      // If a custom renderer is provided, use its output
      const markerConfig = customMarkerIcon(place, context);
      // Return the icon property from the MarkerConfig object
      return markerConfig.icon;
    }

    // Default marker icon logic if no custom renderer is provided
    const color = categoryColors[place.category] || categoryColors.default;
    const isSelected = selectedPlace?.id === place.id;

    return {
      path: google.maps.SymbolPath.CIRCLE,
      fillColor: color,
      fillOpacity: isSelected ? 1.0 : 0.8,
      strokeColor: '#FFFFFF',
      strokeWeight: isSelected ? 3 : 2,
      scale: isSelected ? 12 : 8,
      anchor: new google.maps.Point(0, 0),
    };
  }, [selectedPlace, customMarkerIcon, mode, places?.length]); // Added dependencies


  // Handle marker click
  const handleMarkerClick = useCallback((place: VisitedPlace) => {
    setSelectedPlace(place);
    onMarkerClick?.(place);
  }, [setSelectedPlace, onMarkerClick]);

  // Handle info window close
  const handleInfoWindowClose = useCallback(() => {
    setSelectedPlace(null);
    onInfoWindowClose?.();
  }, [setSelectedPlace, onInfoWindowClose]);

  // Memoize markers to prevent unnecessary re-renders
  const markers = useMemo(() => {
    if (!shouldShowMarkers || !places || !places.length) {
      return null;
    }

    return places.map((place, index) => { // Added index
      const context = createMarkerContext(place, index); // Create context for each marker
      return (
        <Marker
          key={place.id}
          position={{
            lat: place.coordinates.latitude,
            lng: place.coordinates.longitude,
          }}
          icon={createMarkerIcon(place, context)} // Pass context to createMarkerIcon
          onClick={() => handleMarkerClick(place)}
          animation={
            selectedPlace?.id === place.id
              ? google.maps.Animation.BOUNCE
              : undefined
          }
          title={place.name}
        />
      );
    });
  }, [places, shouldShowMarkers, createMarkerIcon, handleMarkerClick, selectedPlace, createMarkerContext]); // Added createMarkerContext dependency


  // Info window for selected place
  const infoWindow = useMemo(() => {
    if (!showInfoWindow || !selectedPlace) {
      return null;
    }

    return (
      <InfoWindow
        position={{
          lat: selectedPlace.coordinates.latitude,
          lng: selectedPlace.coordinates.longitude,
        }}
        onCloseClick={handleInfoWindowClose}
        options={{
          pixelOffset: new google.maps.Size(0, -40),
          maxWidth: 300,
        }}
      >
        <div className="p-3 max-w-xs fifa-info-window">
          {/* FIFA-styled info window */}
          <div className="flex items-start gap-3">
            {/* Assuming selectedPlace.icon is a string or can be rendered */}
            <div className="text-2xl">{selectedPlace.icon}</div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-1 fifa-info-title">
                {selectedPlace.name}
              </h3>
              <p className="text-sm text-gray-600 mb-2 fifa-info-description">
                {selectedPlace.description.en}
              </p>
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <span className="px-2 py-1 bg-gray-100 rounded-full fifa-category-badge">
                  {selectedPlace.category}
                </span>
                {selectedPlace.rating && selectedPlace.rating > 0 && (
                  <span className="flex items-center gap-1">
                    <span className="text-yellow-500">★</span>
                    <span>{selectedPlace.rating}/5</span>
                  </span>
                )}
              </div>
              {selectedPlace.city && (
                <p className="text-xs text-gray-500 mt-1">
                  {selectedPlace.city}, {selectedPlace.region}
                </p>
              )}
            </div>
          </div>
        </div>
      </InfoWindow>
    );
  }, [selectedPlace, showInfoWindow, handleInfoWindowClose]);

  if (!shouldShowMarkers) {
    return null;
  }

  return (
    <div className={className}>
      {markers}
      {infoWindow}
    </div>
  );
}

// Helper function to get category color
export function getCategoryColor(category: string): string {
  return categoryColors[category] || categoryColors.default;
}

// Helper function to create custom marker icons
export function createCustomMarkerIcon(
  category: string,
  isSelected: boolean = false,
  customColor?: string
): google.maps.Symbol {
  const color = customColor || getCategoryColor(category);

  return {
    path: google.maps.SymbolPath.CIRCLE,
    fillColor: color,
    fillOpacity: isSelected ? 1.0 : 0.8,
    strokeColor: '#FFFFFF',
    strokeWeight: isSelected ? 3 : 2,
    scale: isSelected ? 12 : 8,
    anchor: new google.maps.Point(0, 0),
  };
}

// Helper function for location marker (current position)
export function createLocationMarker(accuracy: number = 50): google.maps.Symbol {
  return {
    path: google.maps.SymbolPath.CIRCLE,
    fillColor: '#FFD700', // FIFA Gold
    fillOpacity: 0.9,
    strokeColor: '#DC2626', // FIFA Red border
    strokeWeight: 3,
    scale: accuracy <= 50 ? 12 : 8,
    anchor: new google.maps.Point(0, 0),
  };
}
