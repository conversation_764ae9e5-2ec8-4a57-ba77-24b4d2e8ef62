import React, { useEffect, useRef, useState } from 'react';
import type { BaseMarkerConfig, BaseMapFeatures, MapContextState } from '~/types/maps';
import type { MapFeature } from '~/hooks/useMapFeatures';
import type { VisitedPlace } from '~/types/wanderlust';
import type { MarkerConfig, MarkerContext } from '~/types/maps';
import type { EnhancedTravelRoute } from '~/types/routes-v2'; // Import route types

export interface BaseMapComponentProps {
  features?: MapFeature[];
  children?: React.ReactNode;
  className?: string;
  customMarkerRenderer?: (place: VisitedPlace, context: MarkerContext) => MarkerConfig;
  onPlaceSelect?: (place: VisitedPlace) => void;
  showTrafficLayer?: boolean;
  center?: { lat: number; lng: number };
  zoom?: number;
  mapTypeId?: google.maps.MapTypeId;
  options?: google.maps.MapOptions;
  onMapLoad?: () => void;
  onLoad?: (map: google.maps.Map) => void;
  onClick?: (e: google.maps.MapMouseEvent) => void;
  onError?: (error: Error) => void;
  // Add props for route data and places
  currentRoute: EnhancedTravelRoute | null;
  places: VisitedPlace[];
}

export function BaseMapComponent({
  features = ['markers'],
  children,
  className,
  customMarkerRenderer,
  onPlaceSelect,
  showTrafficLayer,
  center,
  zoom = 12,
  options,
  onLoad,
  onClick,
  currentRoute, // Receive current route data
  places, // Receive places data for markers
}: BaseMapComponentProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const routePolylineRef = useRef<google.maps.Polyline | null>(null);
  const markersRef = useRef<google.maps.Marker[]>([]);
  const trafficLayerRef = useRef<google.maps.TrafficLayer | null>(null);

  // Effect for map initialization
  useEffect(() => {
    if (!mapRef.current) return;

    // Check if Google Maps API is loaded
    if (!window.google) {
      console.error('Google Maps API not loaded.');
      // Optionally call onError
      onError?.(new Error('Google Maps API not loaded.'));
      return;
    }

    const mapOptions: google.maps.MapOptions = {
      center: center || { lat: 0, lng: 0 },
      zoom: zoom,
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      ...options,
      // Disable default UI controls to use custom ones if needed
      disableDefaultUI: true,
      gestureHandling: 'greedy', // Use greedy gesture handling
    };

    const map = new window.google.maps.Map(mapRef.current, mapOptions);
    mapInstanceRef.current = map;

    // Add click listener
    map.addListener('click', (e: google.maps.MapMouseEvent) => {
      onClick?.(e);
    });

    // Call onLoad callback
    onLoad?.(map);

    // Cleanup function
    return () => {
      // Dispose of map instance if necessary (though often not strictly required by API)
      // More importantly, remove listeners to prevent memory leaks
      window.google.maps.event.clearInstanceListeners(map);
      mapInstanceRef.current = null;
    };
  }, [center, zoom, mapTypeId, options, onLoad]); // Re-run if these props change

  // Effect for rendering route polyline
  useEffect(() => {
    const map = mapInstanceRef.current;
    if (!map) return;

    const shouldShowRoute = features?.includes('routes') && currentRoute;

    if (shouldShowRoute) {
      // Extract polyline points from the route
      const path = currentRoute.legs?.flatMap(leg =>
        leg.steps?.map(step =>
          step.polyline?.points ? google.maps.geometry.encoding.decodePath(step.polyline.points) : []
        ).flat()
      ).filter(point => point); // Filter out any null/undefined points

      if (path && path.length > 0) {
        if (routePolylineRef.current) {
          // Update existing polyline
          routePolylineRef.current.setPath(path);
        } else {
          // Create new polyline
          routePolylineRef.current = new google.maps.Polyline({
            path: path,
            geodesic: true,
            strokeColor: '#FF0000', // Example color (FIFA red)
            strokeOpacity: 0.8,
            strokeWeight: 6,
            map: map,
          });
        }
      } else {
        // If route data is available but no path, clear existing polyline
        if (routePolylineRef.current) {
          routePolylineRef.current.setMap(null);
          routePolylineRef.current = null;
        }
      }
    } else {
      // If route feature is disabled or no route data, clear polyline
      if (routePolylineRef.current) {
        routePolylineRef.current.setMap(null);
        routePolylineRef.current = null;
      }
    }
  }, [currentRoute, features]); // Re-run when route or features change

  // Effect for rendering markers
  useEffect(() => {
    const map = mapInstanceRef.current;
    if (!map) return;

    const shouldShowMarkers = features?.includes('markers') && places && places.length > 0;

    // Clear existing markers
    markersRef.current.forEach(marker => marker.setMap(null));
    markersRef.current = [];

    if (shouldShowMarkers) {
      places.forEach((place, index) => {
        const markerContext: MarkerContext = {
          mode: 'route-planning',
          isSelected: false,
          index,
          totalCount: places?.length ?? 0,
          isStart: false,
          isEnd: false,
          waypointData: undefined
          index // index is optional in MarkerContext
        };

        // Use custom renderer if provided, otherwise use a default
        // NOTE: The 'totalCount' property in MarkerContext is causing a TypeScript error.
        // This requires updating the MarkerContext type definition in ~/types/maps.
        const markerOptions: google.maps.MarkerOptions = customMarkerRenderer
          ? {
              ...customMarkerRenderer(place, markerContext),
              position: { lat: place.coordinates.latitude, lng: place.coordinates.longitude },
              title: place.name,
            }
          : {
              position: { lat: place.coordinates.latitude, lng: place.coordinates.longitude },
              title: place.name,
            };

        const marker = new google.maps.Marker({
          ...markerOptions,
          map: map,
        });

        // Add click listener for markers
        if (onPlaceSelect) {
          marker.addListener('click', () => {
            onPlaceSelect(place);
          });
        }

        markersRef.current.push(marker);
      });
    }
  }, [places, features, customMarkerRenderer, onPlaceSelect]); // Re-run when places, features, or renderer change

  // Effect for traffic layer
  useEffect(() => {
    const map = mapInstanceRef.current;
    if (!map) return;

    const shouldShowTraffic = features?.includes('traffic') && showTrafficLayer;

    if (shouldShowTraffic) {
      if (!trafficLayerRef.current) {
        trafficLayerRef.current = new google.maps.TrafficLayer();
      }
      trafficLayerRef.current.setMap(map);
    } else {
      if (trafficLayerRef.current) {
        trafficLayerRef.current.setMap(null);
      }
    }
  }, [features, showTrafficLayer]); // Re-run when features or showTrafficLayer change


  return (
    <div className={className}>
      {/* Map Container */}
      <div id="map" ref={mapRef} style={{ width: '100%', height: '100%' }}>
        {children}
      </div>
    </div>
  );
}
