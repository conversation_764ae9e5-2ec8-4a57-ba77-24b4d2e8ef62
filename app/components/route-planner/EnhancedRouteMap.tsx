/**
 * Enhanced Route Map Component
 *
 * Demonstrates the new BaseMapComponent with route planning specialization.
 * Uses the unified place management and enhanced search capabilities.
 */

import React, { useCallback, useMemo } from 'react';
import { BaseMapComponent, type MarkerContext, type MarkerConfig } from '~/components/maps/BaseMapComponent';
import { EnhancedMapProvider } from '~/components/maps/EnhancedMapProvider';
import { useUnifiedPlaceManagement } from '~/hooks/useUnifiedPlaceManagement';
import { useWanderlustStore } from '~/stores/wanderlust';
import { useRoutesV2 } from '~/hooks/useRoutesV2'; // Import useRoutesV2
import { GoogleMapsErrorBoundary } from '~/components/wanderlust/GoogleMapsErrorBoundary';
import { DemoMap } from '~/components/wanderlust/DemoMap';
import type { VisitedPlace, CityRegion } from '~/types/wanderlust';

interface EnhancedRouteMapProps {
  regions?: CityRegion[];
  onMapLoad?: (map: google.maps.Map) => void;
  onWaypointAdd?: (place: VisitedPlace) => void;
  onWaypointRemove?: (placeId: string) => void;
  className?: string;
}

export function EnhancedRouteMap({
  regions = [],
  onMapLoad,
  onWaypointAdd,
  onWaypointRemove,
  className,
}: EnhancedRouteMapProps) {
  const { itinerary } = useWanderlustStore();
  const { currentRoute } = useRoutesV2(); // Get currentRoute from useRoutesV2

  // Use unified place management for route planning
  const placeManagement = useUnifiedPlaceManagement({
    mode: 'route-planning',
    enableWaypoints: true,
    enableReordering: true,
    maxPlaces: 25,
    enableDuplicateCheck: true,
  });

  // Custom marker renderer for route waypoints
  const routeWaypointRenderer = useCallback((place: VisitedPlace, context: MarkerContext): MarkerConfig => {
    const { index = 0, totalCount } = context;
    const isStart = index === 0;
    const isEnd = index === totalCount - 1;
    
    // FIFA Club World Cup 2025™ colors for route waypoints
    const color = isStart ? '#10B981' : isEnd ? '#EF4444' : '#FFD700';
    const label = isStart ? 'S' : isEnd ? 'E' : (index + 1).toString();
    const textColor = isStart || isEnd ? '#FFF' : '#000';

    return {
      icon: {
        url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
          <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
            <circle cx="20" cy="20" r="18" fill="${color}" stroke="#000" stroke-width="2"/>
            <text x="20" y="26" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="${textColor}">
              ${label}
            </text>
          </svg>
        `)}`,
        scaledSize: new google.maps.Size(40, 40),
        anchor: new google.maps.Point(20, 20),
      },
      animation: context.isSelected ? google.maps.Animation.BOUNCE : undefined,
      zIndex: isStart || isEnd ? 1000 : 500,
      title: `${label}: ${place.name}`,
    };
  }, []);

  // Handle waypoint selection
  const handleWaypointSelect = useCallback((place: VisitedPlace) => {
    placeManagement.actions.selectPlace(place);
    console.log('🗺️ Waypoint selected:', place.name);
  }, [placeManagement.actions]);

  // Handle map load with route-specific setup
  const handleMapLoad = useCallback((map: google.maps.Map) => {
    console.log('🗺️ Enhanced route planner map loaded', {
      waypointsCount: itinerary.length,
      mapContainer: map.getDiv(),
    });

    // Fit bounds to show all waypoints if we have any
    if (itinerary.length > 1) {
      const bounds = new google.maps.LatLngBounds();
      itinerary.forEach(place => {
        bounds.extend({
          lat: place.coordinates.latitude,
          lng: place.coordinates.longitude,
        });
      });
      map.fitBounds(bounds, { top: 50, bottom: 50, left: 50, right: 50 });
    }

    onMapLoad?.(map);
  }, [itinerary, onMapLoad]);

  // Handle place addition from search
  const handlePlaceAdd = useCallback((place: VisitedPlace) => {
    const success = placeManagement.actions.addPlace(place);
    if (success) {
      onWaypointAdd?.(place);
    }
  }, [placeManagement.actions, onWaypointAdd]);

  // Handle place removal
  const handlePlaceRemove = useCallback((placeId: string) => {
    const success = placeManagement.actions.removePlace(placeId);
    if (success) {
      onWaypointRemove?.(placeId);
    }
  }, [placeManagement.actions, onWaypointRemove]);

  // Empty state overlay
  const EmptyStateOverlay = useMemo(() => {
    if (itinerary.length > 0) return null;

    return (
      <div className="absolute inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm z-10 pointer-events-none">
        <div className="text-center text-white">
          <div className="text-4xl mb-4">🗺️</div>
          <h3 className="text-xl font-bold mb-2">Start Planning Your Route</h3>
          <p className="text-white/70 max-w-md">
            Add waypoints using the route builder to begin planning your FIFA Club World Cup 2025™ journey
          </p>
        </div>
      </div>
    );
  }, [itinerary.length]);

  return (
    <EnhancedMapProvider
      features={['markers', 'routes', 'search', 'traffic']}
      regions={regions}
      onMapLoad={handleMapLoad}
      onPlaceAdd={handlePlaceAdd}
      onPlaceRemove={handlePlaceRemove}
    >
      <div className="h-full w-full relative" style={{ minHeight: '400px' }}>
        <GoogleMapsErrorBoundary
          fallback={
            <DemoMap
              onMapLoad={() => handleMapLoad(new google.maps.Map(document.createElement('div')))}
              isLoaded={true}
            />
          }
        >
          <BaseMapComponent
            mode="route-planning"
            features={['markers', 'routes', 'traffic']}
            onMapLoad={handleMapLoad}
            onPlaceSelect={handleWaypointSelect}
            customMarkerRenderer={routeWaypointRenderer}
            enableSearch={true}
            enableGestures={true}
            showTrafficLayer={true}
            className={className}
            currentRoute={currentRoute} // Pass currentRoute
            places={itinerary} // Pass itinerary as places
          />

          {/* Empty State Overlay */}
          {EmptyStateOverlay}
        </GoogleMapsErrorBoundary>
      </div>
    </EnhancedMapProvider>
  );
}

// Export a wrapper that provides backward compatibility
export function EnhancedRouteMapWithProvider({
  regions = [],
  onMapLoad,
  onWaypointAdd,
  onWaypointRemove,
  className,
}: EnhancedRouteMapProps) {
  return (
    <EnhancedRouteMap
      regions={regions}
      onMapLoad={onMapLoad}
      onWaypointAdd={onWaypointAdd}
      onWaypointRemove={onWaypointRemove}
      className={className}
    />
  );
}

// Example usage component for testing
export function RouteMapExample() {
  const handleWaypointAdd = useCallback((place: VisitedPlace) => {
    console.log('✅ Waypoint added:', place.name);
  }, []);

  const handleWaypointRemove = useCallback((placeId: string) => {
    console.log('❌ Waypoint removed:', placeId);
  }, []);

  const handleMapLoad = useCallback((map: google.maps.Map) => {
    console.log('🗺️ Route map loaded and ready');
  }, []);

  return (
    <div className="h-96 w-full">
      <EnhancedRouteMap
        onMapLoad={handleMapLoad}
        onWaypointAdd={handleWaypointAdd}
        onWaypointRemove={handleWaypointRemove}
      />
    </div>
  );
}
