// Simple test to verify <PERSON><PERSON><PERSON><PERSON> can render polylines
import React, { useEffect, useState } from 'react';
import { MapExplorerComponent } from '~/components/shared/MapExplorer';
import { useWanderlustStore } from '~/stores/wanderlust';
import type { TravelRoute } from '~/types/wanderlust';

// Hardcoded test route with polyline from Nashville Airport to Downtown
const testRoute: TravelRoute = {
  id: 'test-route-1',
  name: 'Nashville Test Route',
  waypoints: [
    {
      location: { lat: 36.1245, lng: -86.6782 },
      stopover: true,
      placeId: 'test-airport'
    },
    {
      location: { lat: 36.1627, lng: -86.7816 },
      stopover: true,
      placeId: 'test-downtown'
    }
  ],
  optimized: false,
  travelMode: 'DRIVING',
  estimatedDuration: '25 mins',
  estimatedDistance: '12.5 km',
  // Simple polyline from Nashville Airport to Downtown (encoded)
  polyline: 'u~vdFfxzbOqBgAcAe@}@c@{@e@y@g@w@i@u@k@s@m@q@o@o@q@m@s@k@u@i@w@g@y@e@{@c@}@a@_Aa@aAa@cAa@eAa@gAa@iAa@kAa@mAa@oAa@qAa@sAa@uAa@wAa@yAa@{Aa@}Aa@_Ba@aBa@cBa@eBa@gBa@iBa@kBa@mBa@oBa@qBa@sBa@uBa@wBa@yBa@{Ba@}Ba@_Ca@aCa@cCa@eCa@gCa@iCa@kCa@mCa@oCa@qCa@sCa@uCa@wCa@yCa@{Ca@}Ca@_Da@aDa@cDa@eDa@gDa@iDa@kDa@mDa@oDa@qDa@sDa@uDa@wDa@yDa@{Da@}Da@_Ea@aEa@cEa@eEa@gEa@iEa@kEa@mEa@oEa@qEa@sEa@uEa@wEa@yEa@{Ea@}Ea@_Fa@aFa@cFa@eFa@gFa@iFa@kFa@mFa@oFa@qFa@sFa@uFa@wFa@yFa@{Fa@}Fa@_Ga@aGa@cGa@eGa@gGa@iGa@kGa@mGa@oGa@qGa@sGa@uGa@wGa@yGa@{Ga@}Ga@_Ha@aHa@cHa@eHa@gHa@iHa@kHa@mHa@oHa@qHa@sHa@uHa@wHa@yHa@{Ha@}Ha@_Ia@aIa@cIa@eIa@gIa@iIa@kIa@mIa@oIa@qIa@sIa@uIa@wIa@yIa@{Ia@}Ia@_Ja@aJa@cJa@eJa@gJa@iJa@kJa@mJa@oJa@qJa@sJa@uJa@wJa@yJa@{Ja@}Ja@_Ka@aKa@cKa@eKa@gKa@iKa@kKa@mKa@oKa@qKa@sKa@uKa@wKa@yKa@{Ka@}Ka@_La@aLa@cLa@eLa@gLa@iLa@kLa@mLa@oLa@qLa@sLa@uLa@wLa@yLa@{La@}La@_Ma@aMa@cMa@eMa@gMa@iMa@kMa@mMa@oMa@qMa@sMa@uMa@wMa@yMa@{Ma@}Ma@_Na@aNa@cNa@eNa@gNa@iNa@kNa@mNa@oNa@qNa@sNa@uNa@wNa@yNa@{Na@}Na@_Oa@aOa@cOa@eOa@gOa@iOa@kOa@mOa@oOa@qOa@sOa@uOa@wOa@yOa@{Oa@}Oa@_Pa@aPa@cPa@ePa@gPa@iPa@kPa@mPa@oPa@qPa@sPa@uPa@wPa@yPa@{Pa@}Pa@_Qa@aQa@cQa@eQa@gQa@iQa@kQa@mQa@oQa@qQa@sQa@uQa@wQa@yQa@{Qa@}Qa@_Ra@aRa@cRa@eRa@gRa@iRa@kRa@mRa@oRa@qRa@sRa@uRa@wRa@yRa@{Ra@}Ra@_Sa@aSa@cSa@eSa@gSa@iSa@kSa@mSa@oSa@qSa@sSa@uSa@wSa@ySa@{Sa@}Sa@_Ta@aTa@cTa@eTa@gTa@iTa@kTa@mTa@oTa@qTa@sTa@uTa@wTa@yTa@{Ta@}Ta@_Ua@aUa@cUa@eUa@gUa@iUa@kUa@mUa@oUa@qUa@sUa@uUa@wUa@yUa@{Ua@}Ua@_Va@aVa@cVa@eVa@gVa@iVa@kVa@mVa@oVa@qVa@sVa@uVa@wVa@yVa@{Va@}Va@_Wa@aWa@cWa@eWa@gWa@iWa@kWa@mWa@oWa@qWa@sWa@uWa@wWa@yWa@{Wa@}Wa@_Xa@aXa@cXa@eXa@gXa@iXa@kXa@mXa@oXa@qXa@sXa@uXa@wXa@yXa@{Xa@}Xa@_Ya@aYa@cYa@eYa@gYa@iYa@kYa@mYa@oYa@qYa@sYa@uYa@wYa@yYa@{Ya@}Ya@_Za@aZa@cZa@eZa@gZa@iZa@kZa@mZa@oZa@qZa@sZa@uZa@wZa@yZa@{Za@}Za@_[a@a[a@c[a@e[a@g[a@i[a@k[a@m[a@o[a@q[a@s[a@u[a@w[a@y[a@{[a@}[a@_\\a@a\\a@c\\a@e\\a@g\\a@i\\a@k\\a@m\\a@o\\a@q\\a@s\\a@u\\a@w\\a@y\\a@{\\a@}\\a@_]a@a]a@c]a@e]a@g]a@i]a@k]a@m]a@o]a@q]a@s]a@u]a@w]a@y]a@{]a@}]a@_^a@a^a@c^a@e^a@g^a@i^a@k^a@m^a@o^a@q^a@s^a@u^a@w^a@y^a@{^a@}^a@__a@a_a@c_a@e_a@g_a@i_a@k_a@m_a@o_a@q_a@s_a@u_a@w_a@y_a@{_a@}_a@_`a@a`a@c`a@e`a@g`a@i`a@k`a@m`a@o`a@q`a@s`a@u`a@w`a@y`a@{`a@}`a@_aa@aaa@caa@eaa@gaa@iaa@kaa@maa@oaa@qaa@saa@uaa@waa@yaa@{aa@}aa@_ba@aba@cba@eba@gba@iba@kba@mba@oba@qba@sba@uba@wba@yba@{ba@}ba@_ca@aca@cca@eca@gca@ica@kca@mca@oca@qca@sca@uca@wca@yca@{ca@}ca@_da@ada@cda@eda@gda@ida@kda@mda@oda@qda@sda@uda@wda@yda@{da@}da@_ea@aea@cea@eea@gea@iea@kea@mea@oea@qea@sea@uea@wea@yea@{ea@}ea@_fa@afa@cfa@efa@gfa@ifa@kfa@mfa@ofa@qfa@sfa@ufa@wfa@yfa@{fa@}fa@_ga@aga@cga@ega@gga@iga@kga@mga@oga@qga@sga@uga@wga@yga@{ga@}ga@_ha@aha@cha@eha@gha@iha@kha@mha@oha@qha@sha@uha@wha@yha@{ha@}ha@_ia@aia@cia@eia@gia@iia@kia@mia@oia@qia@sia@uia@wia@yia@{ia@}ia@_ja@aja@cja@eja@gja@ija@kja@mja@oja@qja@sja@uja@wja@yja@{ja@}ja@_ka@aka@cka@eka@gka@ika@kka@mka@oka@qka@ska@uka@wka@yka@{ka@}ka@_la@ala@cla@ela@gla@ila@kla@mla@ola@qla@sla@ula@wla@yla@{la@}la@_ma@ama@cma@ema@gma@ima@kma@mma@oma@qma@sma@uma@wma@yma@{ma@}ma@_na@ana@cna@ena@gna@ina@kna@mna@ona@qna@sna@una@wna@yna@{na@}na@_oa@aoa@coa@eoa@goa@ioa@koa@moa@ooa@qoa@soa@uoa@woa@yoa@{oa@}oa@_pa@apa@cpa@epa@gpa@ipa@kpa@mpa@opa@qpa@spa@upa@wpa@ypa@{pa@}pa@_qa@aqa@cqa@eqa@gqa@iqa@kqa@mqa@oqa@qqa@sqa@uqa@wqa@yqa@{qa@}qa@_ra@ara@cra@era@gra@ira@kra@mra@ora@qra@sra@ura@wra@yra@{ra@}ra@_sa@asa@csa@esa@gsa@isa@ksa@msa@osa@qsa@ssa@usa@wsa@ysa@{sa@}sa@_ta@ata@cta@eta@gta@ita@kta@mta@ota@qta@sta@uta@wta@yta@{ta@}ta@_ua@aua@cua@eua@gua@iua@kua@mua@oua@qua@sua@uua@wua@yua@{ua@}ua@_va@ava@cva@eva@gva@iva@kva@mva@ova@qva@sva@uva@wva@yva@{va@}va@_wa@awa@cwa@ewa@gwa@iwa@kwa@mwa@owa@qwa@swa@uwa@wwa@ywa@{wa@}wa@_xa@axa@cxa@exa@gxa@ixa@kxa@mxa@oxa@qxa@sxa@uxa@wxa@yxa@{xa@}xa@_ya@aya@cya@eya@gya@iya@kya@mya@oya@qya@sya@uya@wya@yya@{ya@}ya@_za@aza@cza@eza@gza@iza@kza@mza@oza@qza@sza@uza@wza@yza@{za@}za@_{a@a{a@c{a@e{a@g{a@i{a@k{a@m{a@o{a@q{a@s{a@u{a@w{a@y{a@{{a@}{a@_|a@a|a@c|a@e|a@g|a@i|a@k|a@m|a@o|a@q|a@s|a@u|a@w|a@y|a@{|a@}|a@_}a@a}a@c}a@e}a@g}a@i}a@k}a@m}a@o}a@q}a@s}a@u}a@w}a@y}a@{}a@}}a@_~a@a~a@c~a@e~a@g~a@i~a@k~a@m~a@o~a@q~a@s~a@u~a@w~a@y~a@{~a@}~a@_a@aa@ca@ea@ga@ia@ka@ma@oa@qa@sa@ua@wa@ya@{a@}a@',
  bounds: {
    north: 36.1627,
    south: 36.1245,
    east: -86.6782,
    west: -86.7816
  },
  legs: [
    {
      startLocation: { lat: 36.1245, lng: -86.6782 },
      endLocation: { lat: 36.1627, lng: -86.7816 },
      distance: { text: '12.5 km', value: 12500 },
      duration: { text: '25 mins', value: 1500 },
      steps: []
    }
  ],
  overview: {
    totalDistance: '12.5 km',
    totalDuration: '25 mins',
    startAddress: 'Nashville International Airport',
    endAddress: 'Downtown Nashville',
    waypointOrder: [0, 1]
  }
};

export default function TestPolyline() {
  const { setCurrentRoute, clearRoute, currentRoute } = useWanderlustStore();
  const [googleMapsStatus, setGoogleMapsStatus] = useState({
    loaded: false,
    geometryAvailable: false,
    canDecodePolyline: false
  });

  // Check Google Maps API status
  useEffect(() => {
    const checkGoogleMaps = () => {
      const loaded = !!(window as any).google?.maps;
      const geometryAvailable = !!(window as any).google?.maps?.geometry?.encoding;
      let canDecodePolyline = false;

      if (geometryAvailable) {
        try {
          // Test with a simple polyline
          const testPolyline = 'u~vdFfxzbOqBgA';
          const decoded = (window as any).google.maps.geometry.encoding.decodePath(testPolyline);
          canDecodePolyline = decoded && decoded.length > 0;
        } catch (error) {
          console.error('Polyline decode test failed:', error);
        }
      }

      setGoogleMapsStatus({
        loaded,
        geometryAvailable,
        canDecodePolyline
      });
    };

    // Check immediately and after delays
    checkGoogleMaps();
    const timeouts = [1000, 3000, 5000].map(delay =>
      setTimeout(checkGoogleMaps, delay)
    );

    return () => timeouts.forEach(clearTimeout);
  }, []);

  // Set the test route on component mount
  useEffect(() => {
    clearRoute(); // Clear any existing route
    setCurrentRoute(testRoute);
  }, [setCurrentRoute, clearRoute]);

  return (
    <div className="h-screen flex flex-col">
      <div className="bg-black text-white p-4">
        <h1 className="text-2xl font-bold mb-2">🧪 Polyline Test</h1>
        <p className="text-sm text-gray-300 mb-2">
          Testing RouteLayer with hardcoded polyline data
        </p>
        <div className="text-xs text-gray-400">
          <p>Route: {testRoute.name}</p>
          <p>Polyline length: {testRoute.polyline?.length || 0} characters</p>
          <p>Waypoints: {testRoute.waypoints.length}</p>
          <p>Distance: {testRoute.estimatedDistance}</p>
          <p>Duration: {testRoute.estimatedDuration}</p>
          <p className={currentRoute ? 'text-green-400' : 'text-red-400'}>
            Store Route: {currentRoute ? '✅ Set' : '❌ Not Set'}
          </p>
          {currentRoute && (
            <div className="text-green-400 space-y-1">
              <p>Store Polyline: {currentRoute.polyline?.length || 0} chars</p>
              <p>Route ID: {currentRoute.id}</p>
              <p>Waypoints: {currentRoute.waypoints?.length || 0}</p>
              <p>Travel Mode: {currentRoute.travelMode}</p>
            </div>
          )}
          <div className="mt-2 border-t border-gray-600 pt-2">
            <p className="font-semibold">Google Maps API Status:</p>
            <p className={googleMapsStatus.loaded ? 'text-green-400' : 'text-red-400'}>
              Maps API: {googleMapsStatus.loaded ? '✅ Loaded' : '❌ Not Loaded'}
            </p>
            <p className={googleMapsStatus.geometryAvailable ? 'text-green-400' : 'text-red-400'}>
              Geometry: {googleMapsStatus.geometryAvailable ? '✅ Available' : '❌ Not Available'}
            </p>
            <p className={googleMapsStatus.canDecodePolyline ? 'text-green-400' : 'text-red-400'}>
              Polyline Decode: {googleMapsStatus.canDecodePolyline ? '✅ Working' : '❌ Failed'}
            </p>
          </div>
        </div>
      </div>

      <div className="flex-1 relative">
        <MapExplorerComponent
          mode="route-planning"
          regions={[]} // Empty regions for this test
        />


      </div>

      <div className="bg-gray-100 p-4 text-sm">
        <h3 className="font-semibold mb-2">Expected Results:</h3>
        <ul className="space-y-1 text-xs">
          <li>• Blue polyline should connect Nashville Airport to Downtown</li>
          <li>• Green marker at start (Airport)</li>
          <li>• Red marker at end (Downtown)</li>
          <li>• Check console for "🛣️ RouteLayer: Rendering state" logs</li>
          <li>• Verify Google Maps geometry encoding is available</li>
        </ul>
      </div>
    </div>
  );
}
